import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// هذا الملف يعمل كوسيط لجميع الطلبات مع حماية مشددة لصفحات الإدارة
export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  const userAgent = request.headers.get('user-agent') || 'unknown';

  // تسجيل طلبات العملاء الخارجيين للمراقبة
  if (userAgent.includes('bot') || userAgent.includes('crawler') || userAgent.includes('ChatGPT')) {
    console.log(`🤖 External client request: ${pathname} from ${userAgent}`);
  }

  // حماية مشددة لجميع صفحات الإدارة
  if (pathname.startsWith('/admin') && pathname !== '/admin/login') {
    // البحث عن التوكن في الكوكيز
    const authToken = request.cookies.get('authToken')?.value;

    console.log('🔍 Checking auth for:', pathname);
    console.log('🔍 Auth token found:', !!authToken);

    // إذا لم يوجد توكن، إظهار صفحة 404
    if (!authToken) {
      console.log(`🚫 Unauthorized access attempt to: ${pathname}`);

      // إنشاء استجابة 404 مخصصة
      return new NextResponse(
        `<!DOCTYPE html>
        <html>
        <head>
          <title>404 - Page Not Found</title>
          <meta charset="utf-8">
          <style>
            body {
              font-family: Arial, sans-serif;
              text-align: center;
              padding: 50px;
              background: #f5f5f5;
            }
            .container {
              max-width: 500px;
              margin: 0 auto;
              background: white;
              padding: 40px;
              border-radius: 10px;
              box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            }
            h1 { color: #333; font-size: 72px; margin: 0; }
            h2 { color: #666; margin: 20px 0; }
            p { color: #888; line-height: 1.6; }
            a { color: #007bff; text-decoration: none; }
            a:hover { text-decoration: underline; }
          </style>
        </head>
        <body>
          <div class="container">
            <h1>404</h1>
            <h2>الصفحة غير موجودة</h2>
            <p>عذراً، الصفحة التي تبحث عنها غير موجودة أو تم نقلها.</p>
            <p><a href="/">العودة إلى الصفحة الرئيسية</a></p>
          </div>
        </body>
        </html>`,
        {
          status: 404,
          headers: {
            'Content-Type': 'text/html; charset=utf-8',
            'X-Frame-Options': 'DENY',
            'X-Content-Type-Options': 'nosniff',
            'Referrer-Policy': 'strict-origin-when-cross-origin'
          }
        }
      );
    }

    // إذا وجد التوكن، التحقق من صحته (اختياري - يمكن تطويره لاحقاً)
    console.log(`✅ Authorized access to: ${pathname}`);
  }

  // إضافة رؤوس الأمان والأداء لجميع الطلبات
  const response = NextResponse.next();

  // Security headers
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  response.headers.set('X-XSS-Protection', '1; mode=block');



  // HSTS للأمان
  if (request.nextUrl.protocol === 'https:') {
    response.headers.set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
  }

  return response;
}

// تكوين المسارات التي سيتم تطبيق الوسيط عليها
export const config = {
  matcher: [
    // تطبيق على جميع المسارات باستثناء المسارات المستثناة
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
};
