'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Locale } from '../../lib/i18n';
import { ProductWithDetails, Category, Subcategory } from '../../types/mysql-database';
import { useSiteSettings } from '../../hooks/useSiteSettings';
import MobileHeader from './MobileHeader';
import MobileBottomNav from './MobileBottomNav';
import MobileToast from './MobileToast';

// نوع عنصر السلة
interface CartItem {
  id: string;
  title: string;
  image: string;
  price: number;
  quantity: number;
}

interface MobileProductDetailPageProps {
  locale: Locale;
  initialProduct?: ProductWithDetails | null;
  initialCategory?: Category | null;
  initialSubcategory?: Subcategory | null;
  productId: string;
}

const MobileProductDetailPage: React.FC<MobileProductDetailPageProps> = ({
  locale,
  initialProduct,
  initialCategory,
  initialSubcategory,
  productId
}) => {
  const { settings } = useSiteSettings();
  
  const [product, setProduct] = useState<ProductWithDetails | null>(initialProduct || null);
  const [category, setCategory] = useState<Category | null>(initialCategory || null);
  const [subcategory, setSubcategory] = useState<Subcategory | null>(initialSubcategory || null);
  const [loading, setLoading] = useState(!initialProduct);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [quantity, setQuantity] = useState(1);
  const [activeTab, setActiveTab] = useState('description');
  const [showImageModal, setShowImageModal] = useState(false);
  const [toast, setToast] = useState<{
    message: string;
    type: 'success' | 'error' | 'info';
    isVisible: boolean;
  }>({
    message: '',
    type: 'success',
    isVisible: false
  });

  // النصوص
  const content = {
    ar: {
      loading: 'جاري التحميل...',
      notFound: 'المنتج غير موجود',
      notFoundMessage: 'المنتج المطلوب غير موجود أو تم حذفه',
      backToProducts: 'العودة للمنتجات',
      available: 'متوفر',
      unavailable: 'غير متوفر',
      code: 'الرمز',
      category: 'الفئة',
      subcategory: 'الفئة الفرعية',
      description: 'الوصف',
      specifications: 'المواصفات',
      features: 'المميزات',
      quantity: 'الكمية',
      addToCart: 'إضافة للسلة',
      whatsapp: 'واتساب',
      share: 'مشاركة',
      favorite: 'المفضلة',
      viewImages: 'عرض الصور',
      closeImages: 'إغلاق',
      addedToCart: 'تم إضافة المنتج للسلة بنجاح',
      errorAddingToCart: 'حدث خطأ في إضافة المنتج للسلة',
      productUnavailable: 'المنتج غير متوفر حالياً',
      relatedProducts: 'منتجات ذات صلة',
      viewAll: 'عرض الكل'
    },
    en: {
      loading: 'Loading...',
      notFound: 'Product Not Found',
      notFoundMessage: 'The requested product was not found or has been deleted',
      backToProducts: 'Back to Products',
      available: 'Available',
      unavailable: 'Unavailable',
      code: 'Code',
      category: 'Category',
      subcategory: 'Subcategory',
      description: 'Description',
      specifications: 'Specifications',
      features: 'Features',
      quantity: 'Quantity',
      addToCart: 'Add to Cart',
      whatsapp: 'WhatsApp',
      share: 'Share',
      favorite: 'Favorite',
      viewImages: 'View Images',
      closeImages: 'Close',
      addedToCart: 'Product added to cart successfully',
      errorAddingToCart: 'Error adding product to cart',
      productUnavailable: 'Product is currently unavailable',
      relatedProducts: 'Related Products',
      viewAll: 'View All'
    }
  };

  const t = content[locale];

  // جلب البيانات عند التحميل
  useEffect(() => {
    const fetchProductData = async () => {
      console.log('🔍 Mobile: بدء جلب بيانات المنتج:', productId);
      console.log('🔍 Mobile: initialProduct:', initialProduct);

      if (initialProduct) {
        console.log('✅ Mobile: استخدام البيانات الأولية');
        return;
      }

      try {
        setLoading(true);
        console.log('🔄 Mobile: جلب تفاصيل المنتج من API...');

        // جلب تفاصيل المنتج
        const productResponse = await fetch(`/api/products?id=${productId}`);
        console.log('📡 Mobile: استجابة API المنتج:', productResponse.status, productResponse.ok);

        if (productResponse.ok) {
          const productResult = await productResponse.json();
          console.log('📦 Mobile: نتيجة API المنتج:', productResult);

          if (productResult.success && productResult.data) {
            console.log('✅ Mobile: تم جلب بيانات المنتج بنجاح');
            setProduct(productResult.data);

            // جلب بيانات الفئة إذا كانت متوفرة
            if (productResult.data.category_id) {
              const categoryResponse = await fetch(`/api/categories?id=${productResult.data.category_id}`);
              if (categoryResponse.ok) {
                const categoryResult = await categoryResponse.json();
                if (categoryResult.success && categoryResult.data) {
                  setCategory(categoryResult.data);
                }
              }
            }

            // جلب بيانات الفئة الفرعية إذا كانت متوفرة
            if (productResult.data.subcategory_id) {
              const subcategoryResponse = await fetch(`/api/subcategories?id=${productResult.data.subcategory_id}`);
              if (subcategoryResponse.ok) {
                const subcategoryResult = await subcategoryResponse.json();
                if (subcategoryResult.success && subcategoryResult.data) {
                  setSubcategory(subcategoryResult.data);
                }
              }
            }
          } else {
            console.error('❌ Mobile: فشل في جلب بيانات المنتج:', productResult);
          }
        } else {
          console.error('❌ Mobile: استجابة API غير صحيحة:', productResponse.status);
        }
      } catch (error) {
        console.error('❌ Mobile: خطأ في جلب بيانات المنتج:', error);
        showToast(
          locale === 'ar' ? 'حدث خطأ في تحميل البيانات' : 'Error loading data',
          'error'
        );
      } finally {
        console.log('🏁 Mobile: انتهاء جلب البيانات، loading = false');
        setLoading(false);
      }
    };

    fetchProductData();
  }, [productId, initialProduct, locale]);

  const showToast = (message: string, type: 'success' | 'error' | 'info' = 'success') => {
    setToast({ message, type, isVisible: true });
  };

  const hideToast = () => {
    setToast(prev => ({ ...prev, isVisible: false }));
  };

  const handleAddToCart = () => {
    if (!product || !product.is_available) {
      showToast(
        locale === 'ar' ? 'المنتج غير متوفر' : 'Product unavailable',
        'error'
      );
      return;
    }

    try {
      const cartItem: CartItem = {
        id: product.id,
        title: locale === 'ar' ? product.title_ar : product.title,
        image: product.images?.[0]?.image_url || '/placeholder-image.jpg',
        price: product.price || 0,
        quantity: quantity
      };

      const existingCart = localStorage.getItem('cart');
      const cart = existingCart ? JSON.parse(existingCart) as CartItem[] : [];

      const existingItemIndex = cart.findIndex((item) => item.id === product.id);

      if (existingItemIndex > -1) {
        cart[existingItemIndex].quantity += quantity;
      } else {
        cart.push(cartItem);
      }

      localStorage.setItem('cart', JSON.stringify(cart));

      // إرسال event لتحديث عداد السلة
      window.dispatchEvent(new Event('cartUpdated'));

      showToast(
        locale === 'ar' ? 'تم إضافة المنتج للسلة بنجاح' : 'Product added to cart successfully',
        'success'
      );
    } catch (error) {
      console.error('Error adding to cart:', error);
      showToast(
        locale === 'ar' ? 'حدث خطأ في إضافة المنتج للسلة' : 'Error adding product to cart',
        'error'
      );
    }
  };



  const handleShare = async () => {
    if (!product) return;

    const productTitle = locale === 'ar' ? product.title_ar : product.title;
    const productUrl = `${window.location.origin}/${locale}/product/${product.id}`;

    if (navigator.share) {
      try {
        await navigator.share({
          title: productTitle,
          text: locale === 'ar' ? product.description_ar : product.description,
          url: productUrl,
        });
      } catch (error) {
        console.error('Error sharing:', error);
      }
    } else {
      // Fallback: copy to clipboard
      try {
        await navigator.clipboard.writeText(productUrl);
        showToast(
          locale === 'ar' ? 'تم نسخ الرابط' : 'Link copied',
          'success'
        );
      } catch (error) {
        console.error('Error copying to clipboard:', error);
      }
    }
  };

  console.log('🎯 Mobile: حالة العرض - loading:', loading, 'product:', !!product);

  if (loading) {
    console.log('⏳ Mobile: عرض شاشة التحميل');
    return (
      <div className="min-h-screen bg-gray-50">
        <MobileHeader
          locale={locale}
          title={t.loading}
          showBackButton={true}
          backUrl={`/${locale}/products`}
        />
        <div className="flex items-center justify-center py-16">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-gray-600">{t.loading}</p>
          </div>
        </div>

      </div>
    );
  }

  if (!product) {
    console.log('❌ Mobile: عرض شاشة المنتج غير موجود');
    return (
      <div className="min-h-screen bg-gray-50">
        <MobileHeader
          locale={locale}
          title={t.notFound}
          showBackButton={true}
          backUrl={`/${locale}/products`}
        />
        <div className="flex items-center justify-center py-16">
          <div className="text-center px-4">
            <div className="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <i className="ri-error-warning-line text-gray-400 text-3xl"></i>
            </div>
            <h2 className="text-xl font-bold text-gray-900 mb-2">{t.notFound}</h2>
            <p className="text-gray-600 mb-8 text-sm">{t.notFoundMessage}</p>
            <Link
              href={`/${locale}/products`}
              className="bg-primary text-white px-6 py-3 rounded-lg font-semibold inline-flex items-center gap-2"
            >
              <i className="ri-arrow-left-line"></i>
              {t.backToProducts}
            </Link>
          </div>
        </div>

      </div>
    );
  }

  console.log('✅ Mobile: عرض تفاصيل المنتج:', product.title);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile Header */}
      <MobileHeader
        locale={locale}
        title={locale === 'ar' ? product.title_ar : product.title}
        showBackButton={true}
        backUrl={`/${locale}/products`}
      />

      {/* Product Content */}
      <div className="pb-24">
        {/* Modern Hero Image Section */}
        <div className="relative bg-gradient-to-br from-gray-50 to-gray-100 overflow-hidden">
          {/* Main Product Image */}
          <div className="relative aspect-square">
            {product.images && product.images.length > 0 ? (
              <div className="relative w-full h-full group cursor-pointer">
                <Image
                  src={product.images[selectedImageIndex]?.image_url || '/placeholder-image.jpg'}
                  alt={locale === 'ar' ? product.title_ar : product.title}
                  fill
                  className="object-cover transition-all duration-700 group-active:scale-105"
                  priority
                  onClick={() => setShowImageModal(true)}
                />

                {/* Elegant Gradient Overlays */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/10 via-transparent to-transparent pointer-events-none"></div>
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-transparent to-black/5 pointer-events-none"></div>

                {/* Floating Action Buttons */}
                <div className="absolute top-3 right-3 flex flex-col gap-2">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      setShowImageModal(true);
                    }}
                    className="w-9 h-9 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center shadow-lg active:scale-95 transition-all duration-200"
                  >
                    <i className="ri-fullscreen-line text-gray-700 text-sm"></i>
                  </button>
                  <button
                    onClick={handleShare}
                    className="w-9 h-9 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center shadow-lg active:scale-95 transition-all duration-200"
                  >
                    <i className="ri-share-line text-gray-700 text-sm"></i>
                  </button>
                  <button className="w-9 h-9 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center shadow-lg active:scale-95 transition-all duration-200">
                    <i className="ri-heart-line text-gray-700 text-sm"></i>
                  </button>
                  <a
                    href={`https://wa.me/${settings?.communicationSettings?.whatsapp?.businessNumber || '+966599252259'}?text=${encodeURIComponent(
                      locale === 'ar'
                        ? `مرحباً، أريد الاستفسار عن هذا المنتج: ${product.title_ar}`
                        : `Hello, I would like to inquire about this product: ${product.title}`
                    )}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="w-9 h-9 bg-green-500 rounded-full flex items-center justify-center shadow-lg active:scale-95 transition-all duration-200"
                  >
                    <i className="ri-whatsapp-line text-white text-sm"></i>
                  </a>
                  <button
                    onClick={handleAddToCart}
                    disabled={!product.is_available}
                    className={`w-9 h-9 rounded-full flex items-center justify-center shadow-lg active:scale-95 transition-all duration-200 ${
                      product.is_available
                        ? 'bg-primary text-white'
                        : 'bg-gray-300 text-gray-500'
                    }`}
                  >
                    <i className="ri-shopping-cart-line text-sm"></i>
                  </button>
                </div>

                {/* Availability Badge - Modern Design */}
                <div className="absolute top-4 left-4">
                  <div className={`flex items-center gap-2 px-4 py-2 rounded-full backdrop-blur-md shadow-lg border transition-all duration-300 ${
                    product.is_available
                      ? 'bg-emerald-500/90 text-white border-emerald-400/30'
                      : 'bg-red-500/90 text-white border-red-400/30'
                  }`}>
                    <div className={`w-2 h-2 rounded-full animate-pulse ${
                      product.is_available ? 'bg-emerald-200' : 'bg-red-200'
                    }`}></div>
                    <span className="text-sm font-medium">
                      {product.is_available
                        ? (locale === 'ar' ? 'متوفر' : 'Available')
                        : (locale === 'ar' ? 'غير متوفر' : 'Out of Stock')
                      }
                    </span>
                  </div>
                </div>
              </div>
            ) : (
              <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-gray-100 via-gray-50 to-gray-100">
                <div className="text-center text-gray-400">
                  <div className="w-24 h-24 bg-gradient-to-br from-gray-200 to-gray-300 rounded-full flex items-center justify-center mx-auto mb-4 shadow-inner">
                    <i className="ri-image-line text-4xl text-gray-500"></i>
                  </div>
                  <p className="text-sm font-medium">{locale === 'ar' ? 'لا توجد صورة' : 'No Image Available'}</p>
                </div>
              </div>
            )}
          </div>

          {/* Modern Navigation & Indicators */}
          {product.images && product.images.length > 1 && (
            <>
              {/* Beautiful Page Indicators */}
              <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 z-20">
                <div className="flex gap-1.5 bg-black/30 backdrop-blur-sm rounded-full px-3 py-2">
                  {product.images.map((_, index) => (
                    <button
                      key={index}
                      onClick={() => setSelectedImageIndex(index)}
                      className={`transition-all duration-300 rounded-full ${
                        index === selectedImageIndex
                          ? 'w-6 h-2 bg-white shadow-sm'
                          : 'w-2 h-2 bg-white/50 hover:bg-white/70'
                      }`}
                    />
                  ))}
                </div>
              </div>

              {/* Sleek Navigation Arrows */}
              <button
                onClick={() => setSelectedImageIndex(selectedImageIndex > 0 ? selectedImageIndex - 1 : product.images!.length - 1)}
                className="absolute left-4 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-white/10 backdrop-blur-md rounded-full flex items-center justify-center text-white shadow-xl hover:bg-white/20 active:scale-95 transition-all duration-300 border border-white/20"
              >
                <i className="ri-arrow-left-line text-xl"></i>
              </button>
              <button
                onClick={() => setSelectedImageIndex(selectedImageIndex < product.images!.length - 1 ? selectedImageIndex + 1 : 0)}
                className="absolute right-4 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-white/10 backdrop-blur-md rounded-full flex items-center justify-center text-white shadow-xl hover:bg-white/20 active:scale-95 transition-all duration-300 border border-white/20"
              >
                <i className="ri-arrow-right-line text-xl"></i>
              </button>
            </>
          )}
        </div>

        {/* Modern Thumbnail Gallery */}
        {product.images && product.images.length > 1 && (
          <div className="bg-white px-4 py-4 border-b border-gray-100">
            <div className="flex gap-3 overflow-x-auto scrollbar-hide pb-2">
              {product.images.map((image, index) => (
                <button
                  key={index}
                  onClick={() => setSelectedImageIndex(index)}
                  className={`flex-shrink-0 relative transition-all duration-300 ${
                    index === selectedImageIndex
                      ? 'scale-110 z-10'
                      : 'opacity-70 hover:opacity-100 hover:scale-105'
                  }`}
                >
                  <div className={`w-16 h-16 rounded-xl overflow-hidden transition-all duration-300 ${
                    index === selectedImageIndex
                      ? 'ring-3 ring-primary ring-offset-2 shadow-lg'
                      : 'ring-1 ring-gray-200'
                  }`}>
                    <Image
                      src={image.image_url}
                      alt={`${locale === 'ar' ? product.title_ar : product.title} ${index + 1}`}
                      width={64}
                      height={64}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  {index === selectedImageIndex && (
                    <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-primary rounded-full"></div>
                  )}
                </button>
              ))}
            </div>
          </div>
        )}

        {/* Modern Product Info Card */}
        <div className="bg-white mx-4 mt-4 rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
          {/* Header Section */}
          <div className="bg-gradient-to-r from-gray-50 to-white p-6">
            <div className="space-y-4">
              {/* Title & Price */}
              <div className="space-y-2">
                <h1 className="text-lg font-bold text-gray-900 leading-tight">
                  {locale === 'ar' ? product.title_ar : product.title}
                </h1>

                {product.price && (
                  <div className="flex items-baseline gap-2 flex-wrap">
                    <div className="flex items-center gap-1">
                      <span className="text-xl font-bold bg-gradient-to-r from-primary to-purple-600 bg-clip-text text-transparent">
                        {product.price}
                      </span>
                      <span className="text-sm font-medium text-gray-600">
                        {locale === 'ar' ? 'ريال' : 'SAR'}
                      </span>
                    </div>
                    {product.original_price && product.original_price > product.price && (
                      <div className="flex items-center gap-1">
                        <span className="text-sm text-gray-400 line-through">
                          {product.original_price}
                        </span>
                        <span className="bg-red-100 text-red-800 text-xs font-bold px-1.5 py-0.5 rounded-full">
                          {Math.round(((product.original_price - product.price) / product.original_price) * 100)}% {locale === 'ar' ? 'خصم' : 'OFF'}
                        </span>
                      </div>
                    )}
                  </div>
                )}
              </div>

              {/* Product Meta Tags */}
              <div className="flex flex-wrap gap-2">
                <div className="flex items-center gap-2 bg-gray-100 px-3 py-1.5 rounded-full">
                  <i className="ri-barcode-line text-gray-500 text-sm"></i>
                  <span className="text-sm font-medium text-gray-700">
                    {product.id.slice(-8)}
                  </span>
                </div>

                {category && (
                  <div className="flex items-center gap-2 bg-blue-50 px-3 py-1.5 rounded-full">
                    <i className="ri-folder-line text-blue-500 text-sm"></i>
                    <span className="text-sm font-medium text-blue-700">
                      {locale === 'ar' ? category.name_ar : category.name}
                    </span>
                  </div>
                )}

                {subcategory && (
                  <div className="flex items-center gap-2 bg-purple-50 px-3 py-1.5 rounded-full">
                    <i className="ri-folder-2-line text-purple-500 text-sm"></i>
                    <span className="text-sm font-medium text-purple-700">
                      {locale === 'ar' ? subcategory.name_ar : subcategory.name}
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Rating Section */}
          <div className="p-4 bg-gray-50 border-t border-gray-100">
            <div className="flex items-center justify-center gap-2">
              <div className="flex items-center gap-1">
                {[1,2,3,4,5].map((star) => (
                  <i key={star} className="ri-star-fill text-yellow-400 text-sm"></i>
                ))}
              </div>
              <span className="text-sm text-gray-600">(4.8) • {locale === 'ar' ? '127 تقييم' : '127 reviews'}</span>
            </div>
          </div>
        </div>

        {/* Modern Tabs */}
        <div className="mx-4 mt-4 bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
          <div className="flex bg-gray-50">
            {['description', 'specifications', 'features'].map((tab) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                className={`flex-1 py-3 px-3 text-xs font-semibold transition-all duration-300 relative ${
                  activeTab === tab
                    ? 'text-primary bg-white shadow-sm'
                    : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100'
                }`}
              >
                <span className="relative z-10">
                  {locale === 'ar'
                    ? (tab === 'description' ? 'الوصف' : tab === 'specifications' ? 'المواصفات' : 'المميزات')
                    : (tab === 'description' ? 'Description' : tab === 'specifications' ? 'Specifications' : 'Features')
                  }
                </span>
                {activeTab === tab && (
                  <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-primary to-purple-600 rounded-t-full"></div>
                )}
              </button>
            ))}
          </div>

          <div className="p-4">
            {activeTab === 'description' && (
              <div className="space-y-3">
                {(product.description_ar || product.description) ? (
                  <div className="prose prose-sm max-w-none">
                    <p className="text-gray-700 leading-relaxed text-sm">
                      {locale === 'ar' ? product.description_ar : product.description}
                    </p>
                  </div>
                ) : (
                  <div className="text-center py-6">
                    <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
                      <i className="ri-file-text-line text-gray-400 text-lg"></i>
                    </div>
                    <p className="text-gray-500 text-xs">
                      {locale === 'ar'
                        ? 'لا يوجد وصف متاح لهذا المنتج'
                        : 'No description available for this product'
                      }
                    </p>
                  </div>
                )}
              </div>
            )}
            {activeTab === 'specifications' && (
              <div className="space-y-4">
                {product.specifications && product.specifications.length > 0 ? (
                  <div className="space-y-4">
                    {product.specifications.map((spec, index) => (
                      <div key={index} className="bg-gray-50 rounded-xl p-4 border border-gray-100">
                        <div className="flex justify-between items-start">
                          <div className="flex-1">
                            <h4 className="font-semibold text-gray-900 text-sm mb-1">
                              {locale === 'ar' ? spec.spec_key_ar : spec.spec_key}
                            </h4>
                            <p className="text-gray-600 text-sm">
                              {locale === 'ar' ? spec.spec_value_ar : spec.spec_value}
                            </p>
                          </div>
                          <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center ml-3">
                            <i className="ri-information-line text-primary text-sm"></i>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <div className="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <i className="ri-file-list-line text-gray-400 text-3xl"></i>
                    </div>
                    <h3 className="font-semibold text-gray-800 mb-2">
                      {locale === 'ar' ? 'لا توجد مواصفات' : 'No Specifications'}
                    </h3>
                    <p className="text-gray-500 text-sm">
                      {locale === 'ar'
                        ? 'لا توجد مواصفات تقنية متاحة لهذا المنتج'
                        : 'No technical specifications available for this product'
                      }
                    </p>
                  </div>
                )}
              </div>
            )}
            {activeTab === 'features' && (
              <div className="space-y-4">
                {product.features && product.features.length > 0 ? (
                  <div className="space-y-3">
                    {product.features.map((feature, index) => (
                      <div key={index} className="group bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-4 border border-green-100 hover:shadow-md transition-all duration-300">
                        <div className="flex items-start gap-4">
                          <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center flex-shrink-0 shadow-lg group-hover:scale-110 transition-transform duration-300">
                            <i className="ri-check-line text-white text-lg"></i>
                          </div>
                          <div className="flex-1 pt-1">
                            <p className="text-gray-800 font-medium leading-relaxed">
                              {locale === 'ar' ? feature.feature_text_ar : feature.feature_text}
                            </p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <div className="w-20 h-20 bg-gradient-to-br from-yellow-100 to-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <i className="ri-star-line text-orange-500 text-3xl"></i>
                    </div>
                    <h3 className="font-semibold text-gray-800 mb-2">
                      {locale === 'ar' ? 'لا توجد مميزات' : 'No Features'}
                    </h3>
                    <p className="text-gray-500 text-sm">
                      {locale === 'ar'
                        ? 'لا توجد مميزات خاصة متاحة لهذا المنتج'
                        : 'No special features available for this product'
                      }
                    </p>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Quantity Selector - Floating */}
      <div className="fixed bottom-20 right-4 z-30">
        <div className="flex items-center bg-white rounded-full shadow-lg border border-gray-200">
          <button
            onClick={() => setQuantity(Math.max(1, quantity - 1))}
            className="w-10 h-10 flex items-center justify-center text-gray-600 hover:text-primary rounded-l-full transition-all duration-200 active:scale-95"
          >
            <i className="ri-subtract-line text-sm"></i>
          </button>
          <div className="px-3 py-2 min-w-[2.5rem] text-center">
            <span className="text-sm font-bold text-gray-800">{quantity}</span>
          </div>
          <button
            onClick={() => setQuantity(quantity + 1)}
            className="w-10 h-10 flex items-center justify-center text-gray-600 hover:text-primary rounded-r-full transition-all duration-200 active:scale-95"
          >
            <i className="ri-add-line text-sm"></i>
          </button>
        </div>
      </div>

      {/* Image Modal */}
      {showImageModal && product.images && product.images.length > 0 && (
        <div className="fixed inset-0 bg-black z-50 flex items-center justify-center">
          <button
            onClick={() => setShowImageModal(false)}
            className="absolute top-4 right-4 w-10 h-10 bg-white/20 rounded-full flex items-center justify-center text-white z-10"
          >
            <i className="ri-close-line text-xl"></i>
          </button>
          
          <div className="w-full h-full flex items-center justify-center p-4">
            <Image
              src={product.images[selectedImageIndex]?.image_url || '/placeholder-image.jpg'}
              alt={locale === 'ar' ? product.title_ar : product.title}
              width={800}
              height={800}
              className="max-w-full max-h-full object-contain"
            />
          </div>

          {product.images.length > 1 && (
            <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2">
              <div className="flex space-x-2 bg-black/50 rounded-full px-4 py-2">
                {product.images.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setSelectedImageIndex(index)}
                    className={`w-3 h-3 rounded-full transition-colors ${
                      index === selectedImageIndex ? 'bg-white' : 'bg-white/50'
                    }`}
                  />
                ))}
              </div>
            </div>
          )}
        </div>
      )}



      {/* Bottom Navigation */}
      <MobileBottomNav locale={locale} />

      {/* Toast Notification */}
      <MobileToast
        message={toast.message}
        type={toast.type}
        isVisible={toast.isVisible}
        onClose={hideToast}
      />
    </div>
  );
};

export default MobileProductDetailPage;
