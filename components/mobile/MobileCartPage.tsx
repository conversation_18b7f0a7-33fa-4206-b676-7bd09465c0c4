'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Locale } from '../../lib/i18n';
import { useCart } from '../../lib/session-cart';
import MobileHeader from './MobileHeader';
import MobileBottomNav from './MobileBottomNav';
import MobileToast from './MobileToast';

interface MobileCartPageProps {
  locale: Locale;
}

const MobileCartPage: React.FC<MobileCartPageProps> = ({ locale }) => {
  const { cart, updateQuantity, removeFromCart, clearCart, total } = useCart();
  
  const [showForm, setShowForm] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [form, setForm] = useState({
    name: '',
    email: '',
    phone: '',
    company: '',
  });
  const [toast, setToast] = useState<{
    message: string;
    type: 'success' | 'error' | 'info';
    isVisible: boolean;
  }>({
    message: '',
    type: 'success',
    isVisible: false
  });

  const content = {
    ar: {
      title: 'سلة التسوق',
      empty: 'السلة فارغة',
      emptyMessage: 'لم تقم بإضافة أي منتجات إلى السلة بعد',
      continueShopping: 'متابعة التسوق',
      quantity: 'الكمية',
      total: 'المجموع',
      remove: 'حذف',
      clear: 'إفراغ السلة',
      requestQuote: 'طلب عرض سعر',
      name: 'الاسم الكامل',
      email: 'البريد الإلكتروني',
      phone: 'رقم الهاتف',
      company: 'اسم الشركة (اختياري)',
      submit: 'إرسال الطلب',
      cancel: 'إلغاء',
      success: 'تم إرسال طلبك بنجاح! سنتواصل معك قريباً.',
      error: 'حدث خطأ أثناء إرسال الطلب. يرجى المحاولة مرة أخرى.',
      sar: 'ريال'
    },
    en: {
      title: 'Shopping Cart',
      empty: 'Cart is empty',
      emptyMessage: 'You haven\'t added any products to your cart yet',
      continueShopping: 'Continue Shopping',
      quantity: 'Quantity',
      total: 'Total',
      remove: 'Remove',
      clear: 'Clear Cart',
      requestQuote: 'Request Quote',
      name: 'Full Name',
      email: 'Email Address',
      phone: 'Phone Number',
      company: 'Company Name (Optional)',
      submit: 'Submit Request',
      cancel: 'Cancel',
      success: 'Your request has been sent successfully! We will contact you soon.',
      error: 'An error occurred while sending the request. Please try again.',
      sar: 'SAR'
    }
  };

  const t = content[locale];

  const showToast = (message: string, type: 'success' | 'error' | 'info' = 'success') => {
    setToast({ message, type, isVisible: true });
  };

  const hideToast = () => {
    setToast(prev => ({ ...prev, isVisible: false }));
  };

  const handleQuantityChange = (id: string, newQuantity: number) => {
    if (newQuantity < 1) {
      removeFromCart(id);
      showToast(
        locale === 'ar' ? 'تم حذف المنتج من السلة' : 'Product removed from cart',
        'success'
      );
    } else {
      updateQuantity(id, newQuantity);
    }
  };

  const handleRemoveItem = (id: string) => {
    removeFromCart(id);
    showToast(
      locale === 'ar' ? 'تم حذف المنتج من السلة' : 'Product removed from cart',
      'success'
    );
  };

  const handleClearCart = () => {
    clearCart();
    showToast(
      locale === 'ar' ? 'تم إفراغ السلة' : 'Cart cleared',
      'success'
    );
  };

  const handleSubmitQuote = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!form.name || !form.email || !form.phone) {
      showToast(
        locale === 'ar' ? 'يرجى ملء جميع الحقول المطلوبة' : 'Please fill all required fields',
        'error'
      );
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await fetch('/api/quote-requests', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          customerInfo: form,
          products: cart,
          totalAmount: total,
        }),
      });

      if (response.ok) {
        showToast(t.success, 'success');
        setForm({ name: '', email: '', phone: '', company: '' });
        setShowForm(false);
        clearCart();
      } else {
        throw new Error('Failed to submit quote request');
      }
    } catch (error) {
      console.error('Error submitting quote:', error);
      showToast(t.error, 'error');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile Header */}
      <MobileHeader
        locale={locale}
        title={t.title}
        subtitle={cart.length > 0 ? `${cart.length} ${locale === 'ar' ? 'منتج' : 'items'}` : undefined}
        showBackButton={true}
        backUrl={`/${locale}`}
      />

      {/* Cart Content */}
      <div className="px-4 py-4">
        {cart.length === 0 ? (
          /* Empty Cart */
          <div className="text-center py-16">
            <div className="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <i className="ri-shopping-cart-line text-gray-400 text-3xl"></i>
            </div>
            <h2 className="text-xl font-bold text-gray-600 mb-2">
              {t.empty}
            </h2>
            <p className="text-gray-500 mb-8 text-sm">
              {t.emptyMessage}
            </p>
            <Link
              href={`/${locale}/products`}
              className="bg-primary text-white px-6 py-3 rounded-lg font-semibold inline-flex items-center gap-2"
            >
              <i className="ri-shopping-bag-line"></i>
              {t.continueShopping}
            </Link>
          </div>
        ) : (
          /* Cart Items */
          <div className="space-y-4">
            {/* Cart Items List */}
            <div className="space-y-3">
              {cart.map((item) => (
                <div key={item.id} className="bg-white rounded-xl p-4 shadow-sm">
                  <div className="flex items-center gap-3">
                    <div className="relative w-16 h-16 flex-shrink-0">
                      <Image
                        src={item.image || '/api/placeholder?width=64&height=64&text=No Image'}
                        alt={item.title}
                        fill
                        className="object-cover rounded-lg"
                      />
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <h3 className="font-semibold text-gray-800 text-sm line-clamp-1">
                        {item.title}
                      </h3>
                      <p className="text-xs text-gray-600 mt-1">
                        {item.price} {t.sar}
                      </p>
                      
                      {/* Quantity Controls */}
                      <div className="flex items-center gap-2 mt-2">
                        <button
                          onClick={() => handleQuantityChange(item.id, item.quantity - 1)}
                          className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center"
                        >
                          <i className="ri-subtract-line text-gray-600"></i>
                        </button>
                        <span className="text-sm font-medium min-w-[2rem] text-center">
                          {item.quantity}
                        </span>
                        <button
                          onClick={() => handleQuantityChange(item.id, item.quantity + 1)}
                          className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center"
                        >
                          <i className="ri-add-line text-gray-600"></i>
                        </button>
                      </div>
                    </div>
                    
                    <div className="flex flex-col items-end gap-2">
                      <p className="font-semibold text-gray-800 text-sm">
                        {typeof item.price === 'number' ? (item.price * item.quantity).toFixed(2) : (item.price * item.quantity)} {t.sar}
                      </p>
                      <button
                        onClick={() => handleRemoveItem(item.id)}
                        className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center"
                      >
                        <i className="ri-delete-bin-line text-red-600 text-sm"></i>
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Cart Summary */}
            <div className="bg-white rounded-xl p-4 shadow-sm">
              <div className="flex items-center justify-between mb-4">
                <span className="font-semibold text-gray-800">{t.total}:</span>
                <span className="font-bold text-lg text-primary">
                  {typeof total === 'number' ? total.toFixed(2) : total} {t.sar}
                </span>
              </div>
              
              <div className="space-y-3">
                <button
                  onClick={() => setShowForm(true)}
                  className="w-full bg-primary text-white py-3 rounded-lg font-semibold"
                >
                  {t.requestQuote}
                </button>
                
                <button
                  onClick={handleClearCart}
                  className="w-full bg-gray-100 text-gray-700 py-3 rounded-lg font-medium"
                >
                  {t.clear}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Quote Form Modal */}
      {showForm && (
        <div className="fixed inset-0 bg-black/50 z-50 flex items-end">
          <div className="bg-white w-full rounded-t-2xl p-6 animate-slideInUp">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-bold text-gray-800">{t.requestQuote}</h3>
              <button
                onClick={() => setShowForm(false)}
                className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center"
              >
                <i className="ri-close-line text-gray-600"></i>
              </button>
            </div>
            
            <form onSubmit={handleSubmitQuote} className="space-y-4">
              <div>
                <input
                  type="text"
                  placeholder={t.name}
                  value={form.name}
                  onChange={(e) => setForm({ ...form, name: e.target.value })}
                  className="w-full px-4 py-3 bg-gray-100 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary/20"
                  required
                />
              </div>
              
              <div>
                <input
                  type="email"
                  placeholder={t.email}
                  value={form.email}
                  onChange={(e) => setForm({ ...form, email: e.target.value })}
                  className="w-full px-4 py-3 bg-gray-100 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary/20"
                  required
                />
              </div>
              
              <div>
                <input
                  type="tel"
                  placeholder={t.phone}
                  value={form.phone}
                  onChange={(e) => setForm({ ...form, phone: e.target.value })}
                  className="w-full px-4 py-3 bg-gray-100 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary/20"
                  required
                />
              </div>
              
              <div>
                <input
                  type="text"
                  placeholder={t.company}
                  value={form.company}
                  onChange={(e) => setForm({ ...form, company: e.target.value })}
                  className="w-full px-4 py-3 bg-gray-100 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary/20"
                />
              </div>
              
              <div className="flex gap-3 pt-4">
                <button
                  type="button"
                  onClick={() => setShowForm(false)}
                  className="flex-1 bg-gray-100 text-gray-700 py-3 rounded-lg font-medium"
                >
                  {t.cancel}
                </button>
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="flex-1 bg-primary text-white py-3 rounded-lg font-semibold disabled:opacity-50"
                >
                  {isSubmitting ? (
                    <i className="ri-loader-4-line animate-spin"></i>
                  ) : (
                    t.submit
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Bottom Navigation */}
      <MobileBottomNav locale={locale} />

      {/* Toast Notification */}
      <MobileToast
        message={toast.message}
        type={toast.type}
        isVisible={toast.isVisible}
        onClose={hideToast}
      />
    </div>
  );
};

export default MobileCartPage;
