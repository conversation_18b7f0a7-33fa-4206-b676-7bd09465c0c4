'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Locale, getOppositeLocale } from '../lib/i18n';
import { getTranslation } from '../lib/translations';
import { useHeaderSettings, useSiteSettings } from '../hooks/useSiteSettings';
import CategoriesDropdown from './CategoriesDropdown';
import { getCartItemCount } from '../lib/session-cart';



interface NavbarProps {
  locale: Locale;
}

const Navbar: React.FC<NavbarProps> = ({ locale }) => {
  const [cartCount, setCartCount] = useState(0);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [isMounted, setIsMounted] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const pathname = usePathname();

  const t = (key: string) => getTranslation(locale, key as keyof typeof import('../lib/translations').translations.ar);
  const { headerSettings } = useHeaderSettings();
  const { settings } = useSiteSettings();

  useEffect(() => {
    setIsMounted(true);
    const updateCartCount = () => {
      try {
        const count = getCartItemCount();
        setCartCount(count);
      } catch (error) {
        console.error('خطأ في تحديث عدد السلة:', error);
        setCartCount(0);
      }
    };
    updateCartCount();
    window.addEventListener('storage', updateCartCount);
    window.addEventListener('cartUpdated', updateCartCount);
    return () => {
      window.removeEventListener('storage', updateCartCount);
      window.removeEventListener('cartUpdated', updateCartCount);
    };
  }, []);



  // تتبع التمرير لتغيير مظهر الناف بار
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // منع التمرير عند فتح القائمة في الجوال
  useEffect(() => {
    if (mobileMenuOpen) {
      document.body.classList.add('mobile-menu-open');
    } else {
      document.body.classList.remove('mobile-menu-open');
    }

    // تنظيف عند إلغاء التحميل
    return () => {
      document.body.classList.remove('mobile-menu-open');
    };
  }, [mobileMenuOpen]);

  // تبديل اللغة
  const switchLanguage = () => {
    const newLocale = getOppositeLocale(locale);
    let newPath = pathname.replace(`/${locale}`, `/${newLocale}`);

    // التأكد من أن المسار يبدأ بـ locale
    if (!newPath.startsWith(`/${newLocale}`)) {
      newPath = `/${newLocale}${newPath}`;
    }

    // حفظ تفضيل اللغة في localStorage والكوكيز
    localStorage.setItem('preferredLocale', newLocale);
    document.cookie = `preferredLocale=${newLocale}; path=/; max-age=31536000`; // سنة واحدة

    // الانتقال للصفحة الجديدة
    window.location.href = newPath;
  };

  // روابط التنقل من الإعدادات أو الافتراضية
  const navLinks = (headerSettings?.navigationItems?.filter(item => item.isActive).map(item => ({
    key: item.nameEn.toLowerCase(),
    href: item.url.startsWith('/') ? `/${locale}${item.url === '/' ? '' : item.url}` : item.url,
    label: locale === 'ar' ? item.nameAr : item.nameEn
  })) || [
    { href: `/${locale}`, label: t('home'), key: 'home' },
    { href: `/${locale}/products`, label: t('products'), key: 'products' },
    { href: `/${locale}/categories`, label: t('categories'), key: 'categories' },
    { href: `/${locale}/about`, label: t('about'), key: 'about' },
    { href: `/${locale}/contact`, label: t('contact'), key: 'contact' },
  ]);

  // اسم الموقع من الإعدادات
  const siteName = locale === 'ar' ? settings?.siteNameAr : settings?.siteName;
  const displaySiteName = siteName || 'DROOB HAJER';

  // دالة للحصول على الأيقونة المناسبة لكل رابط
  const getIconForLink = (key: string) => {
    const icons: { [key: string]: string } = {
      'home': 'home-4',
      'products': 'shopping-bag-3',
      'categories': 'menu-2',
      'about': 'information',
      'contact': 'customer-service-2',
      'services': 'service',
      'blog': 'article'
    };
    return icons[key] || 'arrow-right';
  };

  return (
    <header
      className={`sticky top-0 z-50 transition-all duration-500 ease-in-out ${
        isScrolled
          ? 'bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-200/50'
          : 'bg-gradient-to-r from-white via-gray-50/80 to-white shadow-lg border-b border-gray-100'
      }`}
      style={{
        position: 'sticky',
        top: 0,
        zIndex: 50,
        willChange: 'transform',
        backfaceVisibility: 'hidden',
        backdropFilter: isScrolled ? 'blur(12px)' : 'none',
        WebkitBackdropFilter: isScrolled ? 'blur(12px)' : 'none'
      }}
    >
      {/* شريط علوي للإعلانات أو العروض */}
      <div className="bg-gradient-to-r from-primary to-secondary text-white text-center py-2 text-sm font-medium">
        <div className="container mx-auto px-4 flex items-center justify-center gap-2">
          <i className="ri-fire-line animate-pulse"></i>
          <span>{locale === 'ar' ? '🔥 عروض خاصة - شحن مجاني للطلبات فوق 100 ريال' : '🔥 Special Offers - Free Shipping Over 100 SAR'}</span>
          <i className="ri-fire-line animate-pulse"></i>
        </div>
      </div>

      <div className={`container mx-auto px-4 transition-all duration-300 ${isScrolled ? 'py-3' : 'py-4'}`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-8">
            {/* شعار الموقع المحسن */}
            <Link
              href={`/${locale}`}
              prefetch={true}
              className="group flex items-center gap-3"
            >
              <div className="relative">
                <div className="w-12 h-12 bg-gradient-to-br from-primary to-secondary rounded-xl flex items-center justify-center text-white font-bold text-xl shadow-lg group-hover:shadow-xl transition-all duration-300 transform group-hover:scale-105">
                  {displaySiteName.charAt(0)}
                </div>
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-yellow-400 rounded-full animate-pulse"></div>
              </div>
              <div className="hidden sm:block">
                <h1 className="text-2xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent group-hover:from-secondary group-hover:to-primary transition-all duration-300">
                  {displaySiteName}
                </h1>
                <p className="text-xs text-gray-500 font-medium">
                  {locale === 'ar' ? 'متجرك الموثوق' : 'Your Trusted Store'}
                </p>
              </div>
            </Link>

            {/* قائمة التنقل المحسنة */}
            <nav className="hidden lg:flex items-center space-x-6 rtl:space-x-reverse">
              {navLinks.filter(link => link.key !== 'categories').map((link) => (
                <React.Fragment key={link.key}>
                  <Link
                    href={link.href}
                    prefetch={true}
                    className={`relative px-4 py-2 rounded-lg font-semibold transition-all duration-300 group nav-link-hover ${
                      pathname === link.href
                        ? 'text-white bg-gradient-to-r from-primary to-secondary shadow-lg'
                        : 'text-gray-700 hover:text-primary hover:bg-primary/5'
                    }`}
                  >
                    <span className="relative z-10">{link.label}</span>
                    {pathname !== link.href && (
                      <span className="absolute inset-0 bg-gradient-to-r from-primary/10 to-secondary/10 rounded-lg scale-0 group-hover:scale-100 transition-transform duration-300"></span>
                    )}
                  </Link>

                  {/* إدراج قائمة الفئات بعد رابط المنتجات */}
                  {link.key === 'products' && (
                    <CategoriesDropdown locale={locale} />
                  )}
                </React.Fragment>
              ))}
            </nav>
          </div>

          <div className="flex items-center gap-4">
            {/* شريط البحث السريع */}
            <div className="hidden md:flex items-center relative">
              <div className="relative">
                <input
                  type="text"
                  placeholder={locale === 'ar' ? 'ابحث عن المنتجات...' : 'Search products...'}
                  className="w-64 px-4 py-2 pl-10 pr-4 bg-gray-50 border border-gray-200 rounded-full text-sm focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary transition-all duration-300"
                />
                <i className="ri-search-line absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
              </div>
            </div>

            {/* مبدل اللغة المحسن */}
            <button
              onClick={switchLanguage}
              className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-gray-50 to-gray-100 hover:from-primary/10 hover:to-secondary/10 text-gray-700 hover:text-primary transition-all duration-300 rounded-full border border-gray-200 hover:border-primary/30 group"
            >
              <div className="w-5 h-5 flex items-center justify-center">
                <i className="ri-global-line group-hover:rotate-12 transition-transform duration-300"></i>
              </div>
              <span className="font-medium text-sm">
                {locale === 'ar' ? 'EN' : 'عر'}
              </span>
            </button>

            {/* سلة التسوق المحسنة */}
            <Link href={`/${locale}/cart`} prefetch={true} className="relative group">
              <div className={`w-12 h-12 flex items-center justify-center bg-gradient-to-br from-primary/10 to-secondary/10 hover:from-primary hover:to-secondary text-gray-700 hover:text-white rounded-full transition-all duration-300 transform group-hover:scale-110 group-hover:shadow-lg ${cartCount > 0 ? 'cart-bounce' : ''}`}>
                <i className="ri-shopping-cart-2-line text-xl"></i>
              </div>
              {isMounted && cartCount > 0 && (
                <span className="absolute -top-2 -right-2 bg-gradient-to-r from-red-500 to-pink-500 text-white text-xs w-6 h-6 flex items-center justify-center rounded-full font-bold shadow-lg animate-bounce">
                  {cartCount > 99 ? '99+' : cartCount}
                </span>
              )}
              <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap">
                {locale === 'ar' ? 'سلة التسوق' : 'Shopping Cart'}
              </div>
            </Link>

            {/* زر القائمة للجوال المحسن */}
            <button
              className={`lg:hidden w-12 h-12 flex items-center justify-center rounded-full transition-all duration-300 transform hover:scale-110 relative z-50 ${
                mobileMenuOpen
                  ? 'bg-gradient-to-br from-red-500 to-red-600 text-white shadow-lg'
                  : 'bg-gradient-to-br from-primary/10 to-secondary/10 hover:from-primary hover:to-secondary text-gray-700 hover:text-white'
              }`}
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              aria-label={locale === 'ar' ? 'فتح القائمة الجانبية' : 'Open mobile menu'}
              style={{ touchAction: 'manipulation' }}
            >
              <i className={`ri-${mobileMenuOpen ? 'close' : 'menu'}-line text-xl transition-transform duration-300 ${mobileMenuOpen ? 'rotate-180' : ''}`}></i>
            </button>
          </div>
        </div>

        {/* القائمة المحسنة للجوال */}
        <div className={`lg:hidden mt-6 transition-all duration-500 ease-in-out overflow-hidden navbar-mobile-menu ${
          mobileMenuOpen ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
        }`}>
          <div className="navbar-mobile-content rounded-2xl shadow-xl border border-gray-100 overflow-hidden navbar-optimized">
            {/* شريط البحث للجوال */}
            <div className="p-4 border-b border-gray-100">
              <div className="relative">
                <input
                  type="text"
                  placeholder={locale === 'ar' ? 'ابحث عن المنتجات...' : 'Search products...'}
                  className="w-full px-4 py-3 pl-12 pr-4 bg-gray-50 border border-gray-200 rounded-xl text-sm focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary transition-all duration-300"
                />
                <i className="ri-search-line absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
              </div>
            </div>

            {/* روابط التنقل للجوال - تشمل رابط الفئات للجوال فقط */}
            <nav className="p-4">
              <div className="space-y-2">
                {navLinks.map((link, index) => (
                  <Link
                    key={link.key}
                    href={link.href}
                    prefetch={true}
                    className={`mobile-menu-item flex items-center gap-4 font-semibold py-4 px-4 rounded-xl transition-all duration-300 transform hover:scale-[1.02] relative z-10 ${
                      pathname === link.href
                        ? 'text-white bg-gradient-to-r from-primary to-secondary shadow-lg'
                        : 'text-gray-700 hover:bg-gradient-to-r hover:from-primary/10 hover:to-secondary/10 hover:text-primary'
                    }`}
                    onClick={(e) => {
                      e.preventDefault();
                      setMobileMenuOpen(false);
                      // تأخير صغير للسماح بإغلاق القائمة أولاً
                      setTimeout(() => {
                        window.location.href = link.href;
                      }, 100);
                    }}
                    style={{
                      animationDelay: `${index * 100}ms`,
                      touchAction: 'manipulation'
                    }}
                  >
                    <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                      pathname === link.href ? 'bg-white/20' : 'bg-gray-100'
                    }`}>
                      <i className={`ri-${getIconForLink(link.key)}-line text-lg`}></i>
                    </div>
                    <span className="flex-1">{link.label}</span>
                    <i className="ri-arrow-right-line text-sm opacity-50"></i>
                  </Link>
                ))}



              </div>

              {/* قسم إضافي للجوال */}
              <div className="mt-6 pt-4 border-t border-gray-200">
                <div className="grid grid-cols-2 gap-3">
                  <Link
                    href={`/${locale}/cart`}
                    prefetch={true}
                    className="flex items-center gap-2 p-3 bg-gradient-to-r from-primary/10 to-secondary/10 rounded-xl text-primary font-medium relative z-10"
                    onClick={(e) => {
                      e.preventDefault();
                      setMobileMenuOpen(false);
                      setTimeout(() => {
                        window.location.href = `/${locale}/cart`;
                      }, 100);
                    }}
                    style={{ touchAction: 'manipulation' }}
                  >
                    <i className="ri-shopping-cart-2-line"></i>
                    <span>{locale === 'ar' ? 'السلة' : 'Cart'}</span>
                    {cartCount > 0 && (
                      <span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full">
                        {cartCount}
                      </span>
                    )}
                  </Link>
                  <button
                    onClick={() => {
                      switchLanguage();
                      setMobileMenuOpen(false);
                    }}
                    className="flex items-center gap-2 p-3 bg-gradient-to-r from-gray-100 to-gray-200 rounded-xl text-gray-700 font-medium"
                  >
                    <i className="ri-global-line"></i>
                    <span>{locale === 'ar' ? 'English' : 'العربية'}</span>
                  </button>
                </div>
              </div>
            </nav>
          </div>
        </div>
      </div>

      {/* خلفية تراكب للقائمة المحمولة */}
      {mobileMenuOpen && (
        <div
          className="navbar-mobile-overlay lg:hidden"
          onClick={() => setMobileMenuOpen(false)}
        />
      )}

      {/* شريط التقدم للتمرير */}
      <div className="absolute bottom-0 left-0 h-1 navbar-progress transform origin-left transition-transform duration-300"
           style={{
             width: '100%',
             transform: `scaleX(${isScrolled ? Math.min(window.scrollY / (document.documentElement.scrollHeight - window.innerHeight), 1) : 0})`
           }}
      />
    </header>
  );
};

export default Navbar;
