'use client';

import React, { useState, useEffect } from 'react';
import { Locale } from '../lib/i18n';
import { Category, ProductWithDetails } from '../types/mysql-database';
import MobileHomePage from './mobile/MobileHomePage';
import Navbar from './Navbar';
import Footer from './Footer';
import HeroSection from './HeroSection';
import FeaturedProducts from './FeaturedProducts';
import CategoriesSection from './CategoriesSection';
import ServicesSection from './ServicesSection';
import PartnersSection from './PartnersSection';
import dynamic from 'next/dynamic';

// تحميل ديناميكي للمكونات غير الضرورية للتحميل الأولي
const WhatsAppButton = dynamic(() => import('./WhatsAppButton'), {
  loading: () => null
});

interface ResponsiveHomePageProps {
  locale: Locale;
  categories?: Category[];
  featuredProducts?: ProductWithDetails[];
}

const ResponsiveHomePage: React.FC<ResponsiveHomePageProps> = ({
  locale,
  categories,
  featuredProducts
}) => {
  const [isMobile, setIsMobile] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const checkDevice = () => {
      try {
        const userAgent = navigator.userAgent.toLowerCase();
        const isMobileDevice = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent);
        const isSmallScreen = window.innerWidth <= 768;

        setIsMobile(isMobileDevice || isSmallScreen);
        setIsLoading(false);
      } catch (error) {
        console.error('Error checking device:', error);
        // في حالة الخطأ، افتراض أنه ليس موبايل
        setIsMobile(false);
        setIsLoading(false);
      }
    };

    // تأخير قصير للتأكد من تحميل DOM
    const timer = setTimeout(checkDevice, 100);

    // مؤقت احتياطي للتأكد من إنهاء حالة التحميل
    const fallbackTimer = setTimeout(() => {
      console.warn('Fallback: forcing loading state to false');
      setIsLoading(false);
    }, 2000);

    const handleResize = () => {
      try {
        const isSmallScreen = window.innerWidth <= 768;
        const userAgent = navigator.userAgent.toLowerCase();
        const isMobileDevice = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent);

        setIsMobile(isMobileDevice || isSmallScreen);
      } catch (error) {
        console.error('Error in resize handler:', error);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => {
      clearTimeout(timer);
      clearTimeout(fallbackTimer);
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-gray-600">
            {locale === 'ar' ? 'جاري التحميل...' : 'Loading...'}
          </p>
        </div>
      </div>
    );
  }

  if (isMobile) {
    return (
      <MobileHomePage
        locale={locale}
        categories={categories}
        featuredProducts={featuredProducts}
      />
    );
  }

  return (
    <>
      <Navbar locale={locale} />
      <main>
        <HeroSection locale={locale} />
        <ServicesSection locale={locale} />
        <CategoriesSection locale={locale} categories={categories} />
        <FeaturedProducts locale={locale} products={featuredProducts} />
        <PartnersSection locale={locale} />
      </main>
      <Footer locale={locale} />
      <WhatsAppButton locale={locale} />
    </>
  );
};

export default ResponsiveHomePage;
