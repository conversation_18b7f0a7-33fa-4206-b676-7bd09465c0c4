{"name": "DROOBHAJER", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "clean": "rimraf .next", "build": "rimraf .next && next build", "start": "next start", "lint": "next lint", "security-check": "node scripts/security-check.js", "migrate-uploads": "node scripts/migrate-uploads.js", "migrate-database": "node scripts/migrate-database.js", "secure-deploy": "node scripts/secure-deploy.js", "backup": "node scripts/backup.js", "test-email": "node scripts/test-email.js", "setup-dirs": "node scripts/setup-directories.js", "setup-contact": "node scripts/setup-contact-info.js", "check-contact": "node scripts/check-contact-info.js", "test-db": "node scripts/test-db-connection.js", "fix-db": "chmod +x scripts/fix-database-connection.sh && ./scripts/fix-database-connection.sh", "generate-sitemap": "node scripts/generate-sitemap.cjs", "type-check": "tsc --noEmit", "build-secure": "npm run security-check && npm run clean && npm run build", "deploy": "npm run secure-deploy", "install-sessions": "node scripts/install-session-system.js", "start:production": "NODE_ENV=production next start -p 3000", "build:production": "NODE_ENV=production npm run build", "deploy:hostinger": "chmod +x deploy.sh && ./deploy.sh", "postbuild": "npm run generate-sitemap", "sitemap": "npm run generate-sitemap"}, "dependencies": {"@sentry/nextjs": "^9.33.0", "@types/formidable": "^3.4.5", "@types/nodemailer": "^6.4.17", "bcryptjs": "^3.0.2", "clsx": "^2.1.0", "crypto-js": "^4.2.0", "dotenv": "^17.0.0", "form-data": "^4.0.3", "formidable": "^3.5.4", "framer-motion": "^12.9.2", "fs-extra": "^11.2.0", "jsonwebtoken": "^9.0.2", "mysql2": "^3.14.1", "next": "15.3.1", "next-i18next": "^15.4.2", "next-seo": "^6.8.0", "next-sitemap": "^4.2.3", "node-fetch": "^3.3.2", "nodemailer": "^7.0.3", "prettier": "^3.6.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "resend": "^4.6.0", "rtl-detect": "^1.1.2", "simple-peer": "^9.11.1", "socket.io": "^4.7.4", "socket.io-client": "^4.7.4", "tailwind-merge": "^2.2.1", "uuid": "^9.0.1", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/bcryptjs": "^2.4.6", "@types/cookie": "^0.6.0", "@types/crypto-js": "^4.2.2", "@types/fs-extra": "^11.0.4", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/simple-peer": "^9.11.8", "@types/uuid": "^9.0.8", "autoprefixer": "^10.4.21", "babel-plugin-import": "^1.13.8", "babel-plugin-styled-components": "^2.1.4", "babel-plugin-transform-react-remove-prop-types": "^0.4.24", "babel-plugin-transform-remove-console": "^6.9.4", "babel-plugin-transform-remove-debugger": "^6.9.4", "concurrently": "^8.2.2", "eslint": "^9", "eslint-config-next": "15.3.1", "postcss": "^8.5.3", "rimraf": "^6.0.1", "tailwindcss": "^3.4.17", "ts-node": "^10.9.2", "typescript": "^5"}}