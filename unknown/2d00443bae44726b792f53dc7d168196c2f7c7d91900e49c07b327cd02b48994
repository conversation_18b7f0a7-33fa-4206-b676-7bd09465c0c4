'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { Locale } from '../../lib/i18n';
import { ProductWithDetails, Subcategory, Category } from '../../types/mysql-database';
import MobileHeader from './MobileHeader';
import MobileBottomNav from './MobileBottomNav';
import MobileToast from './MobileToast';

// نوع عنصر السلة
interface CartItem {
  id: string;
  title: string;
  image: string;
  price: number;
  quantity: number;
}

interface MobileSubcategoryPageProps {
  locale: Locale;
  subcategoryId: string;
  initialProducts?: ProductWithDetails[];
  initialSubcategory?: Subcategory;
  initialCategory?: Category;
}

const MobileSubcategoryPage: React.FC<MobileSubcategoryPageProps> = ({ 
  locale, 
  subcategoryId,
  initialProducts,
  initialSubcategory,
  initialCategory
}) => {
  const router = useRouter();
  const [products, setProducts] = useState<ProductWithDetails[]>(initialProducts || []);
  const [subcategory, setSubcategory] = useState<Subcategory | null>(initialSubcategory || null);
  const [category, setCategory] = useState<Category | null>(initialCategory || null);
  const [loading, setLoading] = useState(!initialProducts);
  const [searchQuery, setSearchQuery] = useState('');
  const [showSearch, setShowSearch] = useState(false);
  const [sortBy, setSortBy] = useState('name');
  const [availabilityFilter, setAvailabilityFilter] = useState('all');
  const [showFilters, setShowFilters] = useState(false);
  const [toast, setToast] = useState<{
    message: string;
    type: 'success' | 'error' | 'info';
    isVisible: boolean;
  }>({
    message: '',
    type: 'success',
    isVisible: false
  });

  // جلب البيانات إذا لم يتم تمريرها
  useEffect(() => {
    if (!initialProducts || !initialSubcategory) {
      const fetchData = async () => {
        try {
          setLoading(true);

          // جلب بيانات الفئة الفرعية
          if (!initialSubcategory) {
            const subcategoryResponse = await fetch(`/api/subcategories?id=${subcategoryId}`);
            if (subcategoryResponse.ok) {
              const subcategoryResult = await subcategoryResponse.json();
              if (subcategoryResult.success && subcategoryResult.data) {
                setSubcategory(subcategoryResult.data);

                // جلب بيانات الفئة الرئيسية
                if (!initialCategory && subcategoryResult.data.category_id) {
                  const categoryResponse = await fetch(`/api/categories?id=${subcategoryResult.data.category_id}`);
                  if (categoryResponse.ok) {
                    const categoryResult = await categoryResponse.json();
                    if (categoryResult.success && categoryResult.data) {
                      setCategory(categoryResult.data);
                    }
                  }
                }
              } else {
                router.push(`/${locale}/products`);
                return;
              }
            }
          }

          // جلب المنتجات
          if (!initialProducts) {
            const productsResponse = await fetch(`/api/products?subcategoryId=${subcategoryId}`);
            if (productsResponse.ok) {
              const productsResult = await productsResponse.json();
              if (productsResult.success && productsResult.data) {
                setProducts(productsResult.data);
              }
            }
          }
        } catch (error) {
          console.error('Error fetching data:', error);
        } finally {
          setLoading(false);
        }
      };

      fetchData();
    }
  }, [subcategoryId, initialProducts, initialSubcategory, initialCategory, locale, router]);

  const showToast = (message: string, type: 'success' | 'error' | 'info' = 'success') => {
    setToast({ message, type, isVisible: true });
  };

  const hideToast = () => {
    setToast(prev => ({ ...prev, isVisible: false }));
  };

  const addToCart = (product: ProductWithDetails) => {
    if (!product.is_available) return;

    try {
      const cartItem: CartItem = {
        id: product.id,
        title: locale === 'ar' ? product.title_ar : product.title,
        image: product.images?.[0]?.image_url || '/placeholder-image.jpg',
        price: product.price || 0,
        quantity: 1
      };

      const existingCart = localStorage.getItem('cart');
      const cart = existingCart ? JSON.parse(existingCart) as CartItem[] : [];
      
      const existingItemIndex = cart.findIndex((item) => item.id === product.id);
      
      if (existingItemIndex > -1) {
        cart[existingItemIndex].quantity += 1;
      } else {
        cart.push(cartItem);
      }

      localStorage.setItem('cart', JSON.stringify(cart));
      
      // إرسال event لتحديث عداد السلة
      window.dispatchEvent(new Event('cartUpdated'));
      
      // إظهار رسالة نجاح
      showToast(
        locale === 'ar' 
          ? 'تم إضافة المنتج للسلة بنجاح' 
          : 'Product added to cart successfully',
        'success'
      );
    } catch (error) {
      console.error('Error adding to cart:', error);
      showToast(
        locale === 'ar' 
          ? 'حدث خطأ في إضافة المنتج للسلة' 
          : 'Error adding product to cart',
        'error'
      );
    }
  };

  const handleSearch = () => {
    if (searchQuery.trim()) {
      // البحث محلياً في المنتجات المحملة
      setShowSearch(false);
    }
  };

  // تصفية وترتيب المنتجات
  const filteredProducts = products.filter(product => {
    // فلتر البحث
    const searchField = locale === 'ar' ? product.title_ar : product.title;
    const matchesSearch = !searchQuery.trim() || searchField.toLowerCase().includes(searchQuery.toLowerCase());
    
    // فلتر التوفر
    const matchesAvailability = availabilityFilter === 'all' || 
      (availabilityFilter === 'available' && product.is_available) ||
      (availabilityFilter === 'unavailable' && !product.is_available);
    
    return matchesSearch && matchesAvailability && product.is_active;
  });

  // ترتيب المنتجات
  const sortedProducts = [...filteredProducts].sort((a, b) => {
    switch (sortBy) {
      case 'name':
        const nameA = locale === 'ar' ? a.title_ar : a.title;
        const nameB = locale === 'ar' ? b.title_ar : b.title;
        return nameA.localeCompare(nameB);
      case 'price_low':
        return (a.price || 0) - (b.price || 0);
      case 'price_high':
        return (b.price || 0) - (a.price || 0);
      case 'newest':
        return new Date(b.created_at || '').getTime() - new Date(a.created_at || '').getTime();
      default:
        return 0;
    }
  });

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-gray-600">
            {locale === 'ar' ? 'جاري تحميل المنتجات...' : 'Loading products...'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile Header */}
      <MobileHeader
        locale={locale}
        title={subcategory ? (locale === 'ar' ? subcategory.name_ar : subcategory.name) : ''}
        subtitle={category ? (locale === 'ar' ? category.name_ar : category.name) : ''}
        showBackButton={true}
        onBackClick={() => router.back()}
        showFilters={true}
        onFiltersToggle={() => setShowFilters(!showFilters)}
        customActions={
          <button
            onClick={() => setShowSearch(!showSearch)}
            className="w-9 h-9 bg-gray-100 rounded-full flex items-center justify-center"
          >
            <i className="ri-search-line text-gray-600"></i>
          </button>
        }
      />

      {/* Search Bar */}
      {showSearch && (
        <div className="px-4 py-3 bg-white border-b border-gray-100">
          <div className="flex items-center gap-2 animate-fadeInUp">
            <div className="flex-1 relative">
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder={locale === 'ar' ? 'ابحث في المنتجات...' : 'Search products...'}
                className="w-full px-4 py-2 bg-gray-100 rounded-full text-sm focus:outline-none focus:ring-2 focus:ring-primary/20"
                onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
              />
              <i className="ri-search-line absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
            </div>
            <button
              onClick={handleSearch}
              className="px-4 py-2 bg-primary text-white rounded-full text-sm font-medium"
            >
              {locale === 'ar' ? 'بحث' : 'Search'}
            </button>
          </div>
        </div>
      )}

      {/* Filters */}
      {showFilters && (
        <div className="px-4 py-3 bg-white border-b border-gray-100">
          <div className="space-y-3 animate-fadeInUp">
            <div className="flex gap-2">
              {/* Sort Filter */}
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="flex-1 px-3 py-2 bg-gray-100 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary/20"
              >
                <option value="name">{locale === 'ar' ? 'الاسم' : 'Name'}</option>
                <option value="price_low">{locale === 'ar' ? 'السعر: من الأقل للأعلى' : 'Price: Low to High'}</option>
                <option value="price_high">{locale === 'ar' ? 'السعر: من الأعلى للأقل' : 'Price: High to Low'}</option>
                <option value="newest">{locale === 'ar' ? 'الأحدث' : 'Newest'}</option>
              </select>

              {/* Availability Filter */}
              <select
                value={availabilityFilter}
                onChange={(e) => setAvailabilityFilter(e.target.value)}
                className="flex-1 px-3 py-2 bg-gray-100 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary/20"
              >
                <option value="all">{locale === 'ar' ? 'جميع المنتجات' : 'All Products'}</option>
                <option value="available">{locale === 'ar' ? 'متوفر' : 'Available'}</option>
                <option value="unavailable">{locale === 'ar' ? 'غير متوفر' : 'Unavailable'}</option>
              </select>
            </div>
          </div>
        </div>
      )}

      {/* Products Count */}
      <div className="px-4 py-3 bg-gradient-to-r from-primary to-purple-600">
        <div className="text-center text-white">
          <p className="text-sm">
            {locale === 'ar' 
              ? `${sortedProducts.length} منتج متاح` 
              : `${sortedProducts.length} products available`
            }
          </p>
        </div>
      </div>

      {/* Products Grid */}
      <div className="px-4 py-4">
        {sortedProducts.length > 0 ? (
          <div className="space-y-4">
            {sortedProducts.map((product) => (
              <div
                key={product.id}
                className="bg-white rounded-xl shadow-sm overflow-hidden border border-gray-100 hover:shadow-md transition-shadow"
              >
                <div className="flex">
                  {/* Product Image */}
                  <Link href={`/${locale}/product/${product.id}`} className="block">
                    <div className="w-24 h-24 relative flex-shrink-0">
                      {product.images?.[0]?.image_url ? (
                        <Image
                          src={product.images[0].image_url}
                          alt={locale === 'ar' ? product.title_ar : product.title}
                          fill
                          className="object-cover rounded-l-xl"
                        />
                      ) : (
                        <div className="w-full h-full bg-gray-100 flex items-center justify-center rounded-l-xl">
                          <i className="ri-image-line text-gray-400 text-xl"></i>
                        </div>
                      )}
                    </div>
                  </Link>

                  {/* Product Info */}
                  <div className="flex-1 p-4">
                    <div className="flex justify-between items-start mb-2">
                      <Link href={`/${locale}/product/${product.id}`} className="flex-1">
                        <h3 className="font-semibold text-gray-900 text-sm mb-1 line-clamp-2 leading-tight">
                          {locale === 'ar' ? product.title_ar : product.title}
                        </h3>
                      </Link>

                      {/* Availability Badge */}
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ml-2 flex-shrink-0 ${
                        product.is_available
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {product.is_available
                          ? (locale === 'ar' ? 'متوفر' : 'Available')
                          : (locale === 'ar' ? 'غير متوفر' : 'Unavailable')
                        }
                      </span>
                    </div>

                    <Link href={`/${locale}/product/${product.id}`}>
                      <p className="text-gray-600 text-xs mb-3 line-clamp-2 leading-relaxed">
                        {locale === 'ar' ? product.description_ar : product.description}
                      </p>
                    </Link>

                    {/* Action Buttons */}
                    <div className="flex items-center justify-between">
                      <Link
                        href={`/${locale}/product/${product.id}`}
                        className="flex items-center gap-1 text-primary text-xs font-medium hover:text-primary/80 transition-colors"
                      >
                        <i className="ri-eye-line"></i>
                        <span>{locale === 'ar' ? 'عرض التفاصيل' : 'View Details'}</span>
                      </Link>

                      <button
                        onClick={(e) => {
                          e.preventDefault();
                          addToCart(product);
                        }}
                        disabled={!product.is_available}
                        className={`flex items-center gap-1 px-3 py-1.5 rounded-lg text-xs font-medium transition-all ${
                          product.is_available
                            ? 'bg-primary text-white hover:bg-primary/90 active:scale-95 shadow-sm'
                            : 'bg-gray-200 text-gray-400 cursor-not-allowed'
                        }`}
                      >
                        <i className="ri-shopping-cart-line"></i>
                        <span>{locale === 'ar' ? 'أضف للسلة' : 'Add to Cart'}</span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-16">
            <i className="ri-shopping-bag-line text-6xl text-gray-400 mb-4"></i>
            <h3 className="text-lg font-semibold text-gray-800 mb-2">
              {locale === 'ar' ? 'لا توجد منتجات' : 'No products found'}
            </h3>
            <p className="text-gray-600 mb-6">
              {locale === 'ar' 
                ? 'لم يتم العثور على منتجات تطابق معايير البحث' 
                : 'No products match your search criteria'
              }
            </p>
            <Link
              href={`/${locale}/products`}
              className="inline-flex items-center gap-2 bg-primary text-white px-6 py-3 rounded-lg font-medium"
            >
              <i className="ri-shopping-bag-line"></i>
              <span>{locale === 'ar' ? 'تصفح جميع المنتجات' : 'Browse All Products'}</span>
            </Link>
          </div>
        )}
      </div>

      {/* Bottom Navigation */}
      <MobileBottomNav locale={locale} />

      {/* Toast Notification */}
      <MobileToast
        message={toast.message}
        type={toast.type}
        isVisible={toast.isVisible}
        onClose={hideToast}
      />
    </div>
  );
};

export default MobileSubcategoryPage;
